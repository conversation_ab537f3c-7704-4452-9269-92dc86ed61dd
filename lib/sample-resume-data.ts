import type { FullResume } from "@/db/schema";

/**
 * Sample resume data for testing templates
 * This provides realistic data to showcase template features
 */
export const createSampleResumeData = (): FullResume => {
	return {
		id: 1,
		title: "Senior Software Engineer Resume",
		phone: "966555555555",
		firstName: "<PERSON>",
		lastName: "<PERSON>",
		jobTitle: "Senior Software Engineer",
		address: "123 Tech Street, Apt 4B",
		email: "<EMAIL>",
		website: "https://sarahjohnson.dev",
		bio: "Experienced Senior Software Engineer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable solutions and mentoring junior developers. Passionate about clean code, agile methodologies, and emerging technologies.",
		birthDate: "1990-03-15",
		city: "San Francisco",
		street: "123 Tech Street",
		country: "USA",
		showPhoto: true,
		photo: "/assets/images/sarah_johnson.jpg",
		templateId: 1,
		createdAt: "2024-01-01T00:00:00Z",
		updatedAt: "2024-01-15T00:00:00Z",
		userId: "user_1",
		colorScheme: "blue",
		fontFamily: "inter",
		spacing: "normal",
		margins: "normal",
		thumbnail: "",

		experiences: [
			{
				id: 1,
				company: "TechCorp Solutions",
				title: "Senior Software Engineer",
				city: "San Francisco",
				country: "USA",
				startDate: "2021-06-01",
				endDate: "2022-08-01",
				isCurrent: 1,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
				description:
					"Lead development of microservices architecture serving 2M+ users daily. Mentor team of 5 junior developers and conduct code reviews. Implemented CI/CD pipelines reducing deployment time by 60%. Technologies: React, Node.js, AWS, Docker, Kubernetes.",
			},
			{
				id: 2,
				company: "StartupXYZ",
				title: "Full Stack Developer",
				city: "San Francisco",
				country: "USA",
				startDate: "2019-03-01",
				endDate: "2021-05-31",
				isCurrent: 0,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 2,
				description:
					"Built and maintained customer-facing web applications using React and Python. Collaborated with product team to define technical requirements. Optimized database queries improving application performance by 40%. Participated in agile development process.",
			},
			{
				id: 3,
				company: "Digital Agency Pro",
				title: "Junior Software Developer",
				city: "Oakland",
				country: "USA",
				startDate: "2017-08-01",
				endDate: "2019-02-28",
				isCurrent: 0,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 3,
				description:
					"Developed responsive websites and web applications for clients. Worked with HTML, CSS, JavaScript, and PHP. Collaborated with designers to implement pixel-perfect UI components. Maintained and updated existing client websites.",
			},
		],

		educations: [
			{
				id: 1,
				city: "Berkeley",
				country: "USA",
				institution: "University of California, Berkeley",
				degree: "Bachelor of Science",
				fieldOfStudy: "Computer Science",
				website: "https://berkeley.edu",
				isCurrent: 0,
				startDate: "2013-09-01",
				endDate: "2017-05-31",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
				description:
					"Relevant coursework: Data Structures, Algorithms, Software Engineering, Database Systems, Computer Networks. GPA: 3.7/4.0",
			},
		],

		projects: [
			{
				id: 1,
				title: "E-commerce Platform",
				client: "Personal Project",
				startDate: "2023-01-01",
				endDate: "2023-06-30",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
				description:
					"Built a full-stack e-commerce platform with React, Node.js, and PostgreSQL. Features include user authentication, payment processing, inventory management, and admin dashboard. Deployed on AWS with auto-scaling capabilities.",
				url: "https://github.com/sarahjohnson/ecommerce-platform",
			},
			{
				id: 2,
				title: "Task Management API",
				client: "Open Source",
				startDate: "2022-09-01",
				endDate: "2022-12-31",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 2,
				description:
					"Developed RESTful API for task management application using Express.js and MongoDB. Implemented JWT authentication, real-time notifications, and comprehensive testing suite. Documentation available on GitHub.",
				url: "https://github.com/sarahjohnson/task-api",
			},
		],

		skills: [
			{
				id: 1,
				name: "JavaScript",
				proficiency: 95,
				category: "Programming Languages",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				name: "TypeScript",
				proficiency: 90,
				category: "Programming Languages",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 3,
				name: "Python",
				proficiency: 85,
				category: "Programming Languages",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 4,
				name: "React",
				proficiency: 95,
				category: "Frontend",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 5,
				name: "Node.js",
				proficiency: 90,
				category: "Backend",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 6,
				name: "Express.js",
				proficiency: 88,
				category: "Backend",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 7,
				name: "PostgreSQL",
				proficiency: 85,
				category: "Databases",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 8,
				name: "MongoDB",
				proficiency: 80,
				category: "Databases",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 9,
				name: "AWS",
				proficiency: 85,
				category: "Cloud",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 10,
				name: "Docker",
				proficiency: 80,
				category: "DevOps",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 11,
				name: "Kubernetes",
				proficiency: 75,
				category: "DevOps",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 12,
				name: "Git",
				proficiency: 95,
				category: "Tools",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
		],

		certifications: [
			{
				id: 1,
				title: "AWS Certified Solutions Architect",
				issuer: "Amazon Web Services",
				dateReceived: "2022-08-15",
				description:
					"Professional-level certification demonstrating expertise in designing distributed systems on AWS.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				url: null,
				sort: 1,
			},
			{
				id: 2,
				title: "Certified Kubernetes Administrator",
				issuer: "Cloud Native Computing Foundation",
				dateReceived: "2023-03-20",
				description:
					"Validates skills in Kubernetes cluster administration and troubleshooting.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				url: null,
				sort: 2,
			},
		],

		awards: [
			{
				id: 1,
				title: "Employee of the Year",
				issuer: "TechCorp Solutions",
				dateReceived: "2023-12-01",
				url: null,
				description:
					"Recognized for outstanding performance and leadership in delivering critical projects.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				title: "Hackathon Winner",
				issuer: "Bay Area Tech Meetup",
				dateReceived: "2022-10-15",
				url: null,
				description:
					"First place in 48-hour hackathon for developing innovative AI-powered solution.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
		],

		languages: [
			{
				id: 1,
				name: "English",
				proficiency: 100,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				name: "Spanish",
				proficiency: 75,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 3,
				name: "French",
				proficiency: 60,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
		],

		references: [
			{
				id: 1,
				name: "Michael Chen",
				company: "TechCorp Solutions",
				position: "Engineering Manager",
				email: "<EMAIL>",
				phone: "+****************",
				description:
					"Direct supervisor for 2+ years. Can speak to technical skills and leadership abilities.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				name: "Lisa Rodriguez",
				company: "StartupXYZ",
				position: "CTO",
				email: "<EMAIL>",
				phone: "+****************",
				description:
					"Former CTO who worked closely on product development and architecture decisions.",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
		],

		hobbies: [
			{
				id: 1,
				name: "Rock Climbing",
				description: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				name: "Photography",
				description: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 2,
			},
			{
				id: 3,
				name: "Open Source Contributing",
				description: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 3,
			},
			{
				id: 4,
				name: "Cooking",
				description: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 4,
			},
		],

		volunteerings: [
			{
				id: 1,
				organization: "Code for Good",
				role: "Volunteer Developer",
				startDate: "2020-01-01",
				endDate: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
				description:
					"Volunteer with local non-profit to build web applications for community organizations. Led development of donation tracking system used by 5+ local charities.",
			},
			{
				id: 2,
				organization: "Girls Who Code",
				role: "Mentor",
				startDate: "2021-09-01",
				endDate: "2023-06-30",
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 2,
				description:
					"Mentored high school students learning programming fundamentals. Conducted weekly coding sessions and career guidance workshops.",
			},
		],
		profiles: [
			{
				id: 1,
				url: "https://linkedin.com/in/sarah-johnson",
				username: "sarah-johnson",
				network: "LinkedIn",
				icon: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 1,
			},
			{
				id: 2,
				url: "https://github.com/sarah-dev",
				username: "sarah-dev",
				network: "GitHub",
				icon: null,
				resumeId: 1,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				sort: 2,
			},
		],
	};
};

export default createSampleResumeData;
