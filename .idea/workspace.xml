<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d4718a93-ab56-429e-a59e-5bed9bb1095b" name="Changes" comment="fix boolean type" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="false" />
    <option name="myAllFileTypesSelected" value="true" />
    <option name="mySelectedFileTypes">
      <set />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="309pB83fVTCznYoXH2SMv58UDZs" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "use-drizzle-and-turso",
    "js.debugger.nextJs.config.created.client": "true",
    "js.debugger.nextJs.config.created.server": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.db:generate.executor": "Run",
    "npm.db:local.executor": "Run",
    "npm.db:migrate.executor": "Run",
    "npm.db:studio.executor": "Run",
    "settings.editor.selected.configurable": "Settings.Biome",
    "ts.external.directory.path": "/Users/<USER>/dev/projects/quickcv/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.db:migrate">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="db:generate" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="db:generate" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="db:local" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="db:local" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="db:migrate" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="db:migrate" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="db:studio" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="db:studio" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.db:migrate" />
        <item itemvalue="npm.db:generate" />
        <item itemvalue="npm.db:studio" />
        <item itemvalue="npm.db:local" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d4718a93-ab56-429e-a59e-5bed9bb1095b" name="Changes" comment="" />
      <created>1753049505124</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753049505124</updated>
      <workItem from="1753049506575" duration="332000" />
      <workItem from="1753050599991" duration="11145000" />
    </task>
    <task id="LOCAL-00001" summary="rename server actions and cleanup">
      <option name="closed" value="true" />
      <created>1753051996522</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753051996522</updated>
    </task>
    <task id="LOCAL-00002" summary="additional prisma cleanup">
      <option name="closed" value="true" />
      <created>1753052645450</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753052645450</updated>
    </task>
    <task id="LOCAL-00003" summary="additional prisma cleanup">
      <option name="closed" value="true" />
      <created>1753053948530</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753053948531</updated>
    </task>
    <task id="LOCAL-00004" summary="full resume type with nested properties">
      <option name="closed" value="true" />
      <created>1753055154164</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753055154164</updated>
    </task>
    <task id="LOCAL-00005" summary="fix build error and introduce fullresume type">
      <option name="closed" value="true" />
      <created>1753056191934</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753056191934</updated>
    </task>
    <task id="LOCAL-00006" summary="fix types and build errors">
      <option name="closed" value="true" />
      <created>1753059604814</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753059604814</updated>
    </task>
    <task id="LOCAL-00007" summary="cleanup migrations">
      <option name="closed" value="true" />
      <created>1753060111301</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753060111301</updated>
    </task>
    <task id="LOCAL-00008" summary="seed database">
      <option name="closed" value="true" />
      <created>1753060615231</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753060615231</updated>
    </task>
    <task id="LOCAL-00009" summary="fix boolean type">
      <option name="closed" value="true" />
      <created>1753061878265</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753061878265</updated>
    </task>
    <option name="localTasksCounter" value="10" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="rename server actions and cleanup" />
    <MESSAGE value="additional prisma cleanup" />
    <MESSAGE value="full resume type with nested properties" />
    <MESSAGE value="fix build error and introduce fullresume type" />
    <MESSAGE value="fix types and build errors" />
    <MESSAGE value="cleanup migrations" />
    <MESSAGE value="seed database" />
    <MESSAGE value="fix boolean type" />
    <option name="LAST_COMMIT_MESSAGE" value="fix boolean type" />
  </component>
</project>