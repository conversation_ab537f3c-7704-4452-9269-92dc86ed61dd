import { NextRequest, NextResponse } from "next/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import { templates, websiteTemplates } from "@/db/schema";
import { staticResumeTemplates, staticWebsiteTemplates } from "@/lib/constants";

export async function POST(request: NextRequest) {
	try {
		// Simple authentication check (optional)
		const authHeader = request.headers.get("authorization");
		const expectedAuth = process.env.SEED_API_SECRET;
		
		if (expectedAuth && authHeader !== `Bearer ${expectedAuth}`) {
			return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
		}

		const results = {
			resumeTemplates: { created: 0, existing: 0 },
			websiteTemplates: { created: 0, existing: 0 }
		};

		// Seed resume templates
		for (const template of staticResumeTemplates) {
			const existing = await db
				.select()
				.from(templates)
				.where(eq(templates.slug, template.slug))
				.limit(1);

			if (existing.length === 0) {
				await db.insert(templates).values(template);
				results.resumeTemplates.created++;
			} else {
				results.resumeTemplates.existing++;
			}
		}

		// Seed website templates
		for (const websiteTemplate of staticWebsiteTemplates) {
			const existing = await db
				.select()
				.from(websiteTemplates)
				.where(eq(websiteTemplates.slug, websiteTemplate.slug))
				.limit(1);

			if (existing.length === 0) {
				await db.insert(websiteTemplates).values(websiteTemplate);
				results.websiteTemplates.created++;
			} else {
				results.websiteTemplates.existing++;
			}
		}

		return NextResponse.json({
			message: "Database seeding completed successfully",
			results
		});

	} catch (error) {
		console.error("Seeding error:", error);
		return NextResponse.json(
			{ 
				error: "Seeding failed", 
				details: error instanceof Error ? error.message : "Unknown error"
			},
			{ status: 500 }
		);
	}
}
