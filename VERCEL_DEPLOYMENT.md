# Vercel Deployment Guide for QuickCV

This guide covers deploying QuickCV to Vercel with automatic database migration and seeding.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Turso Database**: Set up your Turso database
3. **Environment Variables**: Prepare your production environment variables

## Environment Variables Setup

In your Vercel dashboard, go to **Project Settings > Environment Variables** and add:

### Required Database Variables
```
TURSO_CONNECTION_URL=libsql://your-database.turso.io
TURSO_AUTH_TOKEN=your-auth-token-here
```

### Authentication (Clerk)
```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_key_here
CLERK_SECRET_KEY=sk_live_your_secret_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
```

### File Upload (UploadThing)
```
UPLOADTHING_SECRET=sk_live_your_secret_here
UPLOADTHING_APP_ID=your_app_id_here
```

### Optional
```
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NODE_ENV=production
```

## Deployment Methods

### Method 1: Automatic Deployment (Recommended)

1. **Connect Repository**: Connect your GitHub repository to Vercel
2. **Configure Build**: The `vercel.json` is already configured to:
   - Run database migrations
   - Seed the database with templates
   - Build the Next.js application
3. **Deploy**: Push to your main branch to trigger deployment

### Method 2: Manual Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Build Process

The deployment automatically runs:

1. **Install Dependencies**: `bun install`
2. **Database Migration**: `bun run db:migrate`
3. **Database Seeding**: `bun run db:seed`
4. **Application Build**: `bun run build`

## Database Seeding Details

The seeding process will add:

### Resume Templates (12 total)
- Azurill, Bronzor, Chikorita, Ditto
- Gengar, Glalie, Kakuna, Leafish
- Nosepass, Onyx, Pikachu, Rhyhorn

### Website Templates (3 total)
- Rocket, Modern, Elegant

## Troubleshooting

### Build Fails at Migration Step
- Verify `TURSO_CONNECTION_URL` and `TURSO_AUTH_TOKEN` are set correctly
- Check Turso database is accessible from Vercel

### Seeding Fails
- Templates may already exist (this is normal)
- Check database connection and permissions

### Environment Variables Not Working
- Ensure variables are set in Vercel dashboard
- Redeploy after adding new variables

## Manual Seeding (If Needed)

If you need to seed the database manually after deployment:

### Option 1: API Endpoint (Recommended)
An API endpoint is already created at `/app/api/seed/route.ts`.

Call it after deployment:
```bash
curl -X POST https://your-app.vercel.app/api/seed
```

Or with authentication (if SEED_API_SECRET is set):
```bash
curl -X POST https://your-app.vercel.app/api/seed \
  -H "Authorization: Bearer your-secret-key"
```

### Option 2: Local Script with Production DB
```bash
# Set production environment variables locally
export TURSO_CONNECTION_URL="your-production-url"
export TURSO_AUTH_TOKEN="your-production-token"

# Run seeding
bun run db:seed
```

## Verification

After deployment, verify:

1. **Application loads**: Visit your Vercel URL
2. **Templates available**: Check resume template selection
3. **Database working**: Try creating a resume

## Support

If you encounter issues:
- Check Vercel build logs
- Verify environment variables
- Test database connection locally first
