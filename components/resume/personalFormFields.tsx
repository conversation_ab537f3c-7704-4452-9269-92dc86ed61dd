"use client";

import { Input } from "@heroui/react";
import { useTranslations } from "next-intl";
import React, { useCallback, useState } from "react";
import { Autosave } from "react-autosave";
import { updateResumeForAutosave } from "@/actions/resumes";
import { FullResume } from "@/db/schema";
import CheckboxField from "../CheckboxField";
import DatePickerComponent from "../DatePicker.component";
import { TrixEditorField } from "../TrixEditorField";
import { UserPhotoUploadThing } from "./user-upload-photo-uploadthing";

const PersonalFormFields: React.FC<{ data: FullResume }> = ({ data }) => {
	const t = useTranslations("forms");
	const [formData, setFormData] = useState(data);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const { name, value, type, checked } = e.target;
			const newValue = type === "checkbox" ? checked : value;

			setFormData((prev) => ({ ...prev, [name]: newValue }));
		},
		[],
	);

	const handleFieldChange = useCallback((fieldName: string, value: any) => {
		setFormData((prev) => ({ ...prev, [fieldName]: value }));
	}, []);

	const handleAutoSave = useCallback(async (data: FullResume) => {
		try {
			const result = await updateResumeForAutosave(data);
			return result;
		} catch (error) {
			console.error("Auto-save failed:", error);
			return false;
		}
	}, []);

	return (
		<div className="grid grid-cols-2 gap-4 h-full w-full">
			<Autosave data={formData} interval={2000} onSave={handleAutoSave} />

			<h2 className="col-span-full text-2xl font-bold text-gray-800 dark:text-gray-100">
				{t("personal_information")}
			</h2>

			<div className="col-span-full grid grid-cols-8 gap-2">
				<div className="col-span-4 md:col-span-3 my-2 flex flex-col gap-4 px-2 justify-center items-start w-full">
					<UserPhotoUploadThing
						photo={data.photo}
						resumeId={data.id}
						onPhotoUpdate={(photoUrl) => handleFieldChange("photo", photoUrl)}
					/>
					<CheckboxField
						className={"w-full"}
						defaultSelected={data.showPhoto === 1}
						name="showPhoto"
						onChange={(value) => handleFieldChange("showPhoto", value)}
					>
						{t("showPhoto")}
					</CheckboxField>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-2 col-span-4 md:col-span-5">
					<Input
						label={t("firstName")}
						name="firstName"
						value={formData.firstName ?? ""}
						variant="bordered"
						onChange={handleInputChange}
					/>

					<Input
						label={t("lastName")}
						name="lastName"
						value={formData.lastName ?? ""}
						variant="bordered"
						onChange={handleInputChange}
					/>

					<Input
						label={t("jobTitle")}
						name="jobTitle"
						value={formData.jobTitle ?? ""}
						variant="bordered"
						onChange={handleInputChange}
					/>

					<Input
						label={t("email")}
						name="email"
						type="email"
						value={formData.email ?? ""}
						variant="bordered"
						onChange={handleInputChange}
					/>

					<DatePickerComponent
						defaultValue={formData.birthDate ?? ""}
						label={t("birthDate")}
						name="birthDate"
						onChange={(value) => handleFieldChange("birthDate", value)}
					/>

					<Input
						label={t("website")}
						name="website"
						value={formData.website ?? ""}
						variant="bordered"
						onChange={handleInputChange}
					/>
				</div>
			</div>

			<Input
				label={t("city")}
				name="city"
				value={formData.city ?? ""}
				variant="bordered"
				onChange={handleInputChange}
			/>

			<Input
				label={t("street")}
				name="street"
				value={formData.street ?? ""}
				variant="bordered"
				onChange={handleInputChange}
			/>

			<Input
				label={t("country")}
				name="country"
				value={formData.country ?? ""}
				variant="bordered"
				onChange={handleInputChange}
			/>

			<Input
				label={t("address")}
				name="address"
				value={formData.address ?? ""}
				variant="bordered"
				onChange={handleInputChange}
			/>

			<TrixEditorField
				className="col-span-full"
				id="bio"
				label={t("bio")}
				name="bio"
				value={formData.bio ?? ""}
				onChange={(value) => handleFieldChange("bio", value)}
			/>
		</div>
	);
};

export default PersonalFormFields;
