import Image from "next/image";
import { useLocale, useTranslations } from "next-intl";
import React from "react";
import {
	Certification,
	Education,
	Experience,
	FullResume,
	Language,
	Profile,
	Project,
	Skill,
} from "@/db/schema";
// Base interfaces for template props
export interface TemplateProps {
	resume: FullResume;
	className?: string;
}

// Utility functions for ATS compatibility
export const formatDate = (
	dateString: string,
	showPresent: boolean = false,
	locale: string = "en-US",
	yearOnly: boolean = false,
): string => {
	if (!dateString && showPresent) {
		return locale === "ar" ? "حتى الآن" : "Present";
	}
	if (!dateString) return "";

	const date = new Date(dateString);

	if (yearOnly) {
		return date.getFullYear().toString();
	}

	if (locale === "ar") {
		return date.toLocaleDateString("ar-SA", {
			year: "numeric",
			month: "short",
		});
	}

	return date.toLocaleDateString("en-US", {
		year: "numeric",
		month: "short",
	});
};

export const formatDateRange = (
	startDate: string,
	endDate: string,
	isCurrent: number = 0,
	locale: string = "en-US",
): string => {
	const start = formatDate(startDate, false, locale);
	const end = isCurrent
		? locale === "ar"
			? "حتى الآن"
			: "Present"
		: formatDate(endDate, false, locale);
	return `${start} - ${end}`;
};

export const getFullName = (
	firstName: string,
	lastName: string,
	locale: string = "en-US",
): string => {
	// In Arabic, sometimes family name comes first
	if (locale === "ar" && lastName && firstName) {
		return `${firstName} ${lastName}`.trim();
	}
	return `${firstName} ${lastName}`.trim();
};

// Hook to get current locale for templates
export const useTemplateLocale = () => {
	try {
		const locale = useLocale();
		return locale;
	} catch {
		// Fallback for server-side rendering
		return "en";
	}
};

export const useSectionTranslations = () => {
	const t = useTranslations("forms");
	return {
		summary: t("summary"),
		experience: t("experience"),
		education: t("education"),
		skills: t("skills"),
		languages: t("languages"),
		projects: t("projects"),
		certifications: t("certifications"),
		profiles: t("profiles"),
		references: t("references"),
		volunteering: t("volunteering"),
		awards: t("awards"),
		hobbies: t("hobbies"),
	};
};

export const formatLocation = (city: string, country: string): string => {
	const parts = [city, country].filter(Boolean);
	return parts.join(", ");
};

// Reusable Resume Components

// 1. Resume Section Component
export interface ResumeSectionProps {
	title: string;
	children: React.ReactNode;
	variant?: "default" | "sidebar" | "accent" | "europass";
	accentColor?: string;
	className?: string;
}

export const ResumeSection: React.FC<ResumeSectionProps> = ({
	title,
	children,
	variant = "default",
	accentColor = "#f97316", // orange-500
	className = "",
}) => {
	const baseClasses = "resume-section mb-6";

	const variantStyles = {
		default:
			"section-title text-lg font-bold mb-3 text-gray-900 border-b border-gray-300 pb-1",
		sidebar: "section-title text-sm font-bold mb-3 text-gray-700",
		accent: `section-title text-lg font-bold mb-4 flex items-center text-gray-800`,
		europass:
			"section-title text-lg font-bold mb-4 text-blue-600 border-b-2 border-blue-600 pb-2",
	};

	return (
		<section className={`${baseClasses} ${className}`}>
			<h2
				className={variantStyles[variant]}
				style={variant === "accent" ? { color: accentColor } : {}}
			>
				{variant === "accent" && (
					<span
						className="w-3 h-3 rounded-full me-3"
						style={{ backgroundColor: accentColor }}
					/>
				)}
				{title}
			</h2>
			{children}
		</section>
	);
};

// 2. Experience Item Component
export interface ExperienceItemProps {
	experience: Experience;
	variant?: "standard" | "compact" | "detailed" | "europass";
	showWebsiteIcon?: boolean;
	className?: string;
	locale?: string;
}

export const ExperienceItem: React.FC<ExperienceItemProps> = ({
	experience,
	variant = "standard",
	showWebsiteIcon: _showWebsiteIcon = true,
	className = "",
	locale = "en-US",
}) => {
	const location = formatLocation(
		experience.city || "",
		experience.country || "",
	);
	const dateRange = formatDateRange(
		experience.startDate,
		experience.endDate,
		experience.isCurrent,
		locale,
	);

	if (variant === "europass") {
		return (
			<div className={`experience-item mb-4 flex ${className}`}>
				<div className="w-1/3 text-sm text-blue-500 font-medium pr-4">
					{dateRange}
				</div>
				<div className="flex-1">
					<h3 className="font-bold text-gray-900">{experience.title}</h3>
					<p className="text-gray-700 font-medium">{experience.company}</p>
					{location && <p className="text-gray-600 text-sm">{location}</p>}
					{experience.description && (
						<div
							dangerouslySetInnerHTML={{ __html: experience.description }}
							className="text-gray-700 text-sm mt-2"
						/>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className={`experience-item mb-4 ${className}`}>
			<div className="flex justify-between items-start mb-2">
				<div className="flex-1">
					<h3 className="font-bold text-gray-900 text-base">
						{experience.company}
					</h3>
					<p className="text-gray-700 text-sm font-semibold">
						{experience.title}
					</p>
				</div>
				<div className="text-right text-sm text-gray-600">
					<p>{dateRange}</p>
					{location && <p>{location}</p>}
				</div>
			</div>
			{experience.description && (
				<div
					dangerouslySetInnerHTML={{ __html: experience.description }}
					className="text-gray-700 text-sm mt-2"
				/>
			)}
		</div>
	);
};

// 3. Education Item Component
export interface EducationItemProps {
	education: Education;
	variant?: "standard" | "compact" | "europass";
	className?: string;
	locale?: string;
}

export const EducationItem: React.FC<EducationItemProps> = ({
	education,
	variant = "standard",
	className = "",
	locale = "en-US",
}) => {
	const location = formatLocation(
		education.city || "",
		education.country || "",
	);
	const dateRange = formatDateRange(
		education.startDate || "",
		education.endDate || "",
		education.isCurrent || 0,
		locale,
	);
	const degreeText = education.fieldOfStudy
		? `${education.degree} in ${education.fieldOfStudy}`
		: education.degree;

	if (variant === "europass") {
		return (
			<div className={`education-item mb-4 flex ${className}`}>
				<div className="w-1/3 text-sm text-blue-500 font-medium pr-4">
					{dateRange}
				</div>
				<div className="flex-1">
					<h3 className="font-bold text-gray-900">{degreeText}</h3>
					<p className="text-gray-700 font-medium">{education.institution}</p>
					{location && <p className="text-gray-600 text-sm">{location}</p>}
					{education.description && (
						<div
							dangerouslySetInnerHTML={{ __html: education.description }}
							className="text-gray-700 text-sm mt-2"
						/>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className={`education-item mb-4 ${className}`}>
			<div className="flex justify-between items-start">
				<div>
					<h3 className="font-bold text-gray-900">{education.institution}</h3>
					<p className="text-gray-700 text-sm">{degreeText}</p>
					{location && <p className="text-gray-600 text-sm">{location}</p>}
				</div>
				<div className="text-right text-sm text-gray-600">
					<p>{dateRange}</p>
				</div>
			</div>
			{education.description && (
				<div
					dangerouslySetInnerHTML={{ __html: education.description }}
					className="text-gray-700 text-sm mt-2"
				/>
			)}
		</div>
	);
};

// 4. Skills Section Component
export interface SkillsSectionProps {
	skills: Skill[];
	layout?: "grid" | "list";
	showProficiency?: boolean;
	className?: string;
}

export const SkillsSection: React.FC<SkillsSectionProps> = ({
	skills,
	layout = "grid",
	className = "",
}) => {
	if (!skills || skills.length === 0) return null;

	// Group skills by category
	const skillsByCategory = skills.reduce(
		(acc, skill) => {
			const category = skill.category || "";
			if (!acc[category]) acc[category] = [];
			acc[category].push(skill);
			return acc;
		},
		{} as Record<string, typeof skills>,
	);

	const renderSkillGroup = (
		category: string,
		categorySkills: typeof skills,
	) => {
		const skillNames = categorySkills.map((skill) => skill.name).join(", ");

		return (
			<div key={category} className="mb-3">
				<h4 className="font-semibold text-gray-800 text-sm mb-1">{category}</h4>
				<p className="text-gray-700 text-sm flex flex-wrap text-start">
					{skillNames}
				</p>
			</div>
		);
	};

	return (
		<div
			className={`skills-section ${
				layout == "grid" ? "grid grid-cols-3 gap-2" : "flex-col"
			}  ${className}`}
		>
			{Object.entries(skillsByCategory).map(([category, categorySkills]) =>
				renderSkillGroup(category, categorySkills),
			)}
		</div>
	);
};

// 5. Contact Information Component
export interface ContactInfoProps {
	location?: string;
	phone?: string;
	email: string;
	website?: string;
	variant?: "horizontal" | "vertical" | "compact";
	className?: string;
}

export const ContactInfo: React.FC<ContactInfoProps> = ({
	location,
	phone,
	email,
	website,
	variant = "vertical",
	className = "",
}) => {
	const contactItems = [
		{ icon: "📍", value: location, href: null },
		{ icon: "📞", value: phone, href: phone ? `tel:${phone}` : null },
		{ icon: "✉️", value: email, href: email ? `mailto:${email}` : null },
		{ icon: "🌐", value: website, href: website },
	].filter((item) => item.value);

	const containerClass =
		variant === "horizontal" ? "flex flex-wrap gap-4" : "space-y-2";

	return (
		<div className={`contact-section ${containerClass} ${className}`}>
			{contactItems.map((item, index) => (
				<div key={index} className="flex items-center text-sm">
					<span className="me-2">{item.icon}</span>
					{item.href ? (
						<a className="text-blue-600 hover:text-blue-800" href={item.href}>
							{item.value}
						</a>
					) : (
						<span>{item.value}</span>
					)}
				</div>
			))}
		</div>
	);
};

// 6. Social Profile Component
export interface SocialProfileProps {
	profiles: Profile[];
	layout?: "grid" | "horizontal" | "vertical";
	showNetworkLabel?: boolean;
	className?: string;
}

export const SocialProfile: React.FC<SocialProfileProps> = ({
	profiles,
	layout = "vertical",
	showNetworkLabel = true,
	className = "",
}) => {
	if (!profiles || profiles.length === 0) return null;

	const getProfileIcon = (network: string) => {
		const icons: Record<string, string> = {
			linkedin: "💼",
			github: "🐙",
			stackoverflow: "🔧",
			twitter: "🐦",
			behance: "🎨",
			dribbble: "🏀",
			instagram: "📷",
			facebook: "📘",
			youtube: "📺",
			tiktok: "🎵",
			default: "🔗",
		};
		return icons[network?.toLowerCase()] || icons.default;
	};

	const containerClass =
		layout === "horizontal"
			? "flex flex-wrap gap-3"
			: layout === "grid"
				? "grid grid-cols-2 gap-2"
				: "space-y-2";

	return (
		<div className={`profiles-grid ${containerClass} ${className}`}>
			{profiles.map((profile) => (
				<div key={profile.id} className="flex items-center text-sm">
					<span className="me-2">{getProfileIcon(profile.network)}</span>
					<div>
						<span className="font-medium text-gray-800">
							{profile.username}
						</span>
						{showNetworkLabel && (
							<span className="ms-1 text-gray-600">
								{profile.network ?? ""}
							</span>
						)}
					</div>
				</div>
			))}
		</div>
	);
};

// 7. Language Item Component
export interface LanguageItemProps {
	language: Language;
	showLevel?: boolean;
	showBars?: boolean;
	className?: string;
}

export const LanguageItem: React.FC<LanguageItemProps> = ({
	language,
	showLevel = true,
	showBars = false,
	className = "",
}) => {
	const getProficiencyLevel = (proficiency: number): string => {
		if (proficiency >= 90) return "Native Speaker";
		if (proficiency >= 70) return "Fluent";
		if (proficiency >= 50) return "Intermediate";
		if (proficiency >= 30) return "Basic";
		return "";
	};

	return (
		<div
			className={`language-item flex justify-between items-center mb-2 ${className}`}
		>
			<span className="font-medium text-gray-800">{language.name}</span>
			{showLevel && (
				<span className="text-gray-600 text-sm">
					{getProficiencyLevel(language.proficiency)}
				</span>
			)}
			{showBars && (
				<div className="flex-1 mx-3">
					<div className="w-full bg-gray-200 rounded-full h-2">
						<div
							className="bg-blue-500 h-2 rounded-full"
							style={{ width: `${language.proficiency}%` }}
						/>
					</div>
				</div>
			)}
		</div>
	);
};

// 8. Resume Header Component
export interface ResumeHeaderProps {
	firstName: string;
	lastName: string;
	jobTitle: string;
	photo?: string;
	showPhoto?: boolean;
	layout?: "centered" | "left" | "split";
	variant?: "simple" | "accent" | "background";
	children?: React.ReactNode; // for contact info
	className?: string;
	locale?: string;
}

export const ResumeHeader: React.FC<ResumeHeaderProps> = ({
	firstName,
	lastName,
	jobTitle,
	photo,
	showPhoto = true,
	layout = "centered",
	variant: _variant = "simple",
	children,
	className = "",
	locale = "en-US",
}) => {
	const fullName = getFullName(firstName, lastName, locale);

	const layoutClasses = {
		centered: "text-center",
		left: "text-left",
		split: "flex justify-between items-center",
	};

	return (
		<header
			className={`resume-header ${layoutClasses[layout]} mb-6 ${className}`}
		>
			<div className="flex-1">
				<h1 className="text-3xl font-bold text-gray-900 mb-2">{fullName}</h1>
				<p className="text-xl text-gray-700 mb-4">{jobTitle}</p>
				{children}
			</div>
			{showPhoto && photo && (
				<div className="ms-6">
					<Image
						alt={fullName}
						className="w-24 h-24 rounded-full object-cover"
						height={96}
						src={photo}
						width={96}
					/>
				</div>
			)}
		</header>
	);
};

// 9. Project Item Component
export interface ProjectItemProps {
	project: Project;
	variant?: "standard" | "compact" | "card";
	showClient?: boolean;
	showTechnologies?: boolean;
	className?: string;
}

export const ProjectItem: React.FC<ProjectItemProps> = ({
	project,
	variant = "standard",
	showClient = true,
	showTechnologies: _showTechnologies = true,
	className = "",
}) => {
	const dateRange =
		project.startDate && project.endDate
			? formatDateRange(project.startDate, project.endDate, 0, "en-US")
			: null;

	if (variant === "card") {
		return (
			<div
				className={`project-item mb-4 p-4 border border-gray-200 rounded-lg ${className}`}
			>
				<div className="flex justify-between items-start mb-2">
					<h3 className="font-bold text-gray-900 mb-1">{project.title}</h3>
					{dateRange && (
						<span className="text-gray-600 text-sm">{dateRange}</span>
					)}
				</div>
				{showClient && project.client && (
					<p className="text-gray-700 text-sm font-medium mb-2">
						{project.client}
					</p>
				)}
				{project.description && (
					<div
						dangerouslySetInnerHTML={{ __html: project.description }}
						className="text-gray-700 text-sm mb-2"
					/>
				)}
				{project.url && (
					<p className="text-blue-500 text-sm mt-2">🔗 {project.url}</p>
				)}
			</div>
		);
	}

	return (
		<div className={`project-item mb-4 ${className}`}>
			<div className="flex justify-between items-start mb-1">
				<h3 className="font-bold text-gray-900">{project.title}</h3>
				{dateRange && (
					<span className="text-gray-600 text-sm">{dateRange}</span>
				)}
			</div>
			{showClient && project.client && (
				<p className="text-gray-700 text-sm font-medium mb-1">
					{project.client}
				</p>
			)}
			{project.description && (
				<div
					dangerouslySetInnerHTML={{ __html: project.description }}
					className="text-gray-700 text-sm mb-2"
				/>
			)}
			{project.url && <p className="text-blue-500 text-sm">🔗 {project.url}</p>}
		</div>
	);
};

// 10. Certification Item Component
export interface CertificationItemProps {
	certification: Certification;
	showCredentialId?: boolean;
	className?: string;
}

export const CertificationItem: React.FC<CertificationItemProps> = ({
	certification,
	showCredentialId = true,
	className = "",
}) => {
	const issueDate = certification.dateReceived
		? formatDate(certification.dateReceived, false, "en-US", true)
		: null;

	return (
		<div className={`certification-item mb-3 ${className}`}>
			<div className="flex justify-between items-start">
				<div>
					<h3 className="font-bold text-gray-900">{certification.title}</h3>
					<p className="text-gray-700 text-sm">{certification.issuer}</p>
					<div className="font-bold text-sm text-gray-600">
						{issueDate && <p>{issueDate}</p>}
					</div>
				</div>
			</div>
			{showCredentialId && certification.description && (
				<div
					dangerouslySetInnerHTML={{ __html: certification.description }}
					className="text-gray-700 text-sm mt-2"
				/>
			)}
			{/* {certification.url && (
        <p className="text-blue-500 text-sm mt-1">🔗 View Certificate</p>
      )} */}
		</div>
	);
};

// 11. Professional Summary Component
export interface ProfessionalSummaryProps {
	bio: string;
	variant?: "paragraph" | "bullet" | "highlight";
	className?: string;
}

export const ProfessionalSummary: React.FC<ProfessionalSummaryProps> = ({
	bio,
	variant = "paragraph",
	className = "",
}) => {
	if (!bio) return null;

	const baseClasses = "professional-summary text-gray-700";

	if (variant === "highlight") {
		return (
			<div
				className={`${baseClasses} bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 ${className}`}
			>
				<div
					dangerouslySetInnerHTML={{ __html: bio }}
					className="text-sm leading-relaxed"
				/>
			</div>
		);
	}

	return (
		<div className={`${baseClasses} ${className}`}>
			<div
				dangerouslySetInnerHTML={{ __html: bio }}
				className="text-sm leading-relaxed"
			/>
		</div>
	);
};
