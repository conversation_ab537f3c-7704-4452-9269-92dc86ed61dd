import React from "react";
import {
	formatLocation,
	getFullName,
} from "@/components/resume/templates/base-components";
import {
	WebsiteContact,
	WebsiteEducation,
	WebsiteExperience,
	WebsiteFooter,
	WebsiteHero,
	WebsiteNav,
	WebsiteProjects,
	WebsiteSection,
	WebsiteSkills,
	WebsiteTemplateProps,
} from "./base-components";

export const PortfolioTemplate: React.FC<WebsiteTemplateProps> = ({
	resume,
	website,
	className = "",
}) => {
	const fullName = getFullName(resume.firstName || "", resume.lastName || "");
	const location = formatLocation(resume.city || "", resume.country || "");

	// Navigation sections
	const sections = [
		{ id: "about", label: "About" },
		{ id: "experience", label: "Experience" },
		{ id: "projects", label: "Projects" },
		{ id: "skills", label: "Skills" },
		{ id: "education", label: "Education" },
		{ id: "contact", label: "Contact" },
	];

	return (
		<div className={`portfolio-template min-h-screen bg-gray-50 ${className}`}>
			{/* Navigation */}
			<WebsiteNav fullName={fullName} sections={sections} />

			{/* Hero Section */}
			<WebsiteHero
				firstName={resume.firstName || ""}
				lastName={resume.lastName || ""}
				jobTitle={resume.jobTitle || ""}
				bio={resume.bio}
				photo={resume.photo}
				showPhoto={resume.showPhoto === 1}
				className="bg-gradient-to-br from-blue-50 to-indigo-100"
			/>

			{/* Main Content */}
			<main className="max-w-6xl mx-auto px-4 py-12">
				{/* About Section */}
				<WebsiteSection title="About" id="about" className="mb-16">
					<div className="bg-white p-8 rounded-lg shadow-sm border">
						<div className="text-lg text-gray-700 leading-relaxed">
							{resume.bio ? (
								<div dangerouslySetInnerHTML={{ __html: resume.bio }} />
							) : (
								<p>Professional with expertise in {resume.jobTitle}.</p>
							)}
						</div>
						{location && (
							<div className="mt-6 flex items-center text-gray-600">
								<span className="mr-2">📍</span>
								<span>{location}</span>
							</div>
						)}
					</div>
				</WebsiteSection>

				{/* Experience Section */}
				{resume.experiences && resume.experiences.length > 0 && (
					<WebsiteSection title="Experience" id="experience" className="mb-16">
						<WebsiteExperience experiences={resume.experiences} />
					</WebsiteSection>
				)}

				{/* Projects Section */}
				{resume.projects && resume.projects.length > 0 && (
					<WebsiteSection title="Projects" id="projects" className="mb-16">
						<WebsiteProjects projects={resume.projects} />
					</WebsiteSection>
				)}

				{/* Skills Section */}
				{resume.skills && resume.skills.length > 0 && (
					<WebsiteSection title="Skills" id="skills" className="mb-16">
						<WebsiteSkills skills={resume.skills} />
					</WebsiteSection>
				)}

				{/* Education Section */}
				{resume.educations && resume.educations.length > 0 && (
					<WebsiteSection title="Education" id="education" className="mb-16">
						<WebsiteEducation educations={resume.educations} />
					</WebsiteSection>
				)}

				{/* Contact Section */}
				<WebsiteSection title="Contact" id="contact">
					<WebsiteContact
						email={resume.email}
						website={resume.website}
						location={location}
						className="bg-white p-8 rounded-lg shadow-sm border"
					/>
				</WebsiteSection>
			</main>

			{/* Footer */}
			<WebsiteFooter fullName={fullName} />
		</div>
	);
};
