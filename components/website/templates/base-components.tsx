import Image from "next/image";
import React from "react";

import {
	formatDateRange,
	formatLocation,
	getFullName,
} from "@/components/resume/templates/base-components";
import { FullResume, Skill, Website } from "@/db/schema";

// Base interface for website template props
export interface WebsiteTemplateProps {
	resume: FullResume;
	website: Website;
	className?: string;
}

// Website-specific section component
export interface WebsiteSectionProps {
	title: string;
	children: React.ReactNode;
	className?: string;
	id?: string;
}

export const WebsiteSection: React.FC<WebsiteSectionProps> = ({
	title,
	children,
	className = "",
	id,
}) => {
	return (
		<section className={`website-section mb-8 ${className}`} id={id}>
			<h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
				{title}
			</h2>
			{children}
		</section>
	);
};

// Hero section component for websites
export interface WebsiteHeroProps {
	firstName: string;
	lastName: string;
	jobTitle: string;
	bio?: string;
	photo?: string;
	showPhoto?: boolean;
	className?: string;
}

export const WebsiteHero: React.FC<WebsiteHeroProps> = ({
	firstName,
	lastName,
	jobTitle,
	bio,
	photo,
	showPhoto = true,
	className = "",
}) => {
	const fullName = getFullName(firstName, lastName);
	const displayTitle = jobTitle;
	const displayBio = bio;

	return (
		<section className={`website-hero text-center py-16 px-4 ${className}`}>
			{showPhoto && photo && (
				<div className="mb-6">
					<Image
						alt={fullName}
						className="w-32 h-32 rounded-full object-cover mx-auto shadow-lg"
						height={128}
						src={photo}
						width={128}
					/>
				</div>
			)}
			<h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
				{fullName}
			</h1>
			<p className="text-xl md:text-2xl text-gray-600 mb-6">{displayTitle}</p>
			{displayBio && (
				<div
					dangerouslySetInnerHTML={{ __html: displayBio }}
					className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed"
				/>
			)}
		</section>
	);
};

// Contact section component for websites
export interface WebsiteContactProps {
	email?: string;
	website?: string;
	location?: string;
	className?: string;
}

export const WebsiteContact: React.FC<WebsiteContactProps> = ({
	email,
	website,
	location,
	className = "",
}) => {
	const contactItems = [
		{
			label: "Email",
			value: email,
			href: email ? `mailto:${email}` : null,
			icon: "✉️",
		},
		{ label: "Website", value: website, href: website, icon: "🌐" },
		{ label: "Location", value: location, href: null, icon: "📍" },
	].filter((item) => item.value);

	if (contactItems.length === 0) return null;

	return (
		<section className={`website-contact text-center py-8 ${className}`}>
			<h2 className="text-2xl font-bold text-gray-900 mb-6">Get In Touch</h2>
			<div className="flex flex-wrap justify-center gap-6">
				{contactItems.map((item, index) => (
					<div key={index} className="flex items-center text-lg">
						<span className="mr-2 text-xl">{item.icon}</span>
						{item.href ? (
							<a
								href={item.href}
								className="text-blue-600 hover:text-blue-800 transition-colors"
							>
								{item.value}
							</a>
						) : (
							<span className="text-gray-700">{item.value}</span>
						)}
					</div>
				))}
			</div>
		</section>
	);
};

// Experience timeline component for websites
export interface WebsiteExperienceProps {
	experiences: any[];
	className?: string;
}

export const WebsiteExperience: React.FC<WebsiteExperienceProps> = ({
	experiences,
	className = "",
}) => {
	if (!experiences || experiences.length === 0) return null;

	return (
		<div className={`website-experience ${className}`}>
			<div className="space-y-6">
				{experiences.map((experience) => {
					const location = formatLocation(
						experience.city || "",
						experience.country || "",
					);
					const dateRange = formatDateRange(
						experience.startDate,
						experience.endDate,
						experience.isCurrent,
					);

					return (
						<div
							key={experience.id}
							className="bg-white p-6 rounded-lg shadow-sm border"
						>
							<div className="flex flex-col md:flex-row md:justify-between md:items-start mb-4">
								<div>
									<h3 className="text-xl font-bold text-gray-900 mb-1">
										{experience.title}
									</h3>
									<p className="text-lg text-blue-600 font-semibold mb-1">
										{experience.company}
									</p>
									{location && <p className="text-gray-600">{location}</p>}
								</div>
								<div className="text-gray-600 mt-2 md:mt-0 md:text-right">
									<p className="font-medium">{dateRange}</p>
								</div>
							</div>
							{experience.description && (
								<div
									dangerouslySetInnerHTML={{ __html: experience.description }}
									className="text-gray-700 leading-relaxed"
								/>
							)}
						</div>
					);
				})}
			</div>
		</div>
	);
};

// Skills grid component for websites
export interface WebsiteSkillsProps {
	skills: Skill[];
	className?: string;
}

export const WebsiteSkills: React.FC<WebsiteSkillsProps> = ({
	skills,
	className = "",
}) => {
	if (!skills || skills.length === 0) return null;

	// Group skills by category
	const skillsByCategory = skills.reduce(
		(acc, skill) => {
			const category = skill.category || "";
			if (!acc[category]) acc[category] = [];
			acc[category].push(skill);
			return acc;
		},
		{} as Record<string, typeof skills>,
	);

	return (
		<div className={`website-skills ${className}`}>
			<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
				{Object.entries(skillsByCategory).map(([category, categorySkills]) => (
					<div
						key={category}
						className="bg-white p-6 rounded-lg shadow-sm border"
					>
						<h3 className="text-lg font-bold text-gray-900 mb-3">{category}</h3>
						<div className="flex flex-wrap gap-2">
							{categorySkills.map((skill) => (
								<span
									key={skill.id}
									className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
								>
									{skill.name}
								</span>
							))}
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

// Projects showcase component for websites
export interface WebsiteProjectsProps {
	projects: any[];
	className?: string;
}

export const WebsiteProjects: React.FC<WebsiteProjectsProps> = ({
	projects,
	className = "",
}) => {
	if (!projects || projects.length === 0) return null;

	return (
		<div className={`website-projects ${className}`}>
			<div className="grid md:grid-cols-2 gap-6">
				{projects.map((project) => {
					const dateRange =
						project.startDate && project.endDate
							? formatDateRange(project.startDate, project.endDate, 0, "en-US")
							: null;

					return (
						<div
							key={project.id}
							className="bg-white p-6 rounded-lg shadow-sm border"
						>
							<div className="flex justify-between items-start mb-4">
								<h3 className="text-xl font-bold text-gray-900">
									{project.title}
								</h3>
								{dateRange && (
									<span className="text-gray-600 text-sm">{dateRange}</span>
								)}
							</div>
							{project.client && (
								<p className="text-blue-600 font-semibold mb-2">
									{project.client}
								</p>
							)}
							{project.description && (
								<div
									dangerouslySetInnerHTML={{ __html: project.description }}
									className="text-gray-700 mb-4 leading-relaxed"
								/>
							)}
							{project.url && (
								<a
									href={project.url}
									target="_blank"
									rel="noopener noreferrer"
									className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
								>
									🔗 View Project
								</a>
							)}
						</div>
					);
				})}
			</div>
		</div>
	);
};

// Education component for websites
export interface WebsiteEducationProps {
	educations: any[];
	className?: string;
}

export const WebsiteEducation: React.FC<WebsiteEducationProps> = ({
	educations,
	className = "",
}) => {
	if (!educations || educations.length === 0) return null;

	return (
		<div className={`website-education ${className}`}>
			<div className="space-y-4">
				{educations.map((education) => {
					const location = formatLocation(
						education.city || "",
						education.country || "",
					);
					const dateRange = formatDateRange(
						education.startDate,
						education.endDate,
						education.isCurrent,
					);
					const degreeText = education.field_of_study
						? `${education.degree} in ${education.field_of_study}`
						: education.degree;

					return (
						<div
							key={education.id}
							className="bg-white p-6 rounded-lg shadow-sm border"
						>
							<div className="flex flex-col md:flex-row md:justify-between md:items-start">
								<div>
									<h3 className="text-lg font-bold text-gray-900 mb-1">
										{education.institution}
									</h3>
									<p className="text-blue-600 font-semibold mb-1">
										{degreeText}
									</p>
									{location && <p className="text-gray-600">{location}</p>}
								</div>
								<div className="text-gray-600 mt-2 md:mt-0">
									<p className="font-medium">{dateRange}</p>
								</div>
							</div>
							{education.description && (
								<div
									dangerouslySetInnerHTML={{ __html: education.description }}
									className="text-gray-700 mt-3 leading-relaxed"
								/>
							)}
						</div>
					);
				})}
			</div>
		</div>
	);
};

// Navigation component for websites
export interface WebsiteNavProps {
	fullName: string;
	sections: { id: string; label: string }[];
	className?: string;
}

export const WebsiteNav: React.FC<WebsiteNavProps> = ({
	fullName,
	sections,
	className = "",
}) => {
	return (
		<nav
			className={`website-nav bg-white shadow-sm border-b sticky top-0 z-50 ${className}`}
		>
			<div className="max-w-6xl mx-auto px-4 py-4">
				<div className="flex justify-between items-center">
					<div className="font-bold text-xl text-gray-900">{fullName}</div>
					<div className="hidden md:flex space-x-6">
						{sections.map((section) => (
							<a
								key={section.id}
								href={`#${section.id}`}
								className="text-gray-600 hover:text-gray-900 transition-colors"
							>
								{section.label}
							</a>
						))}
					</div>
				</div>
			</div>
		</nav>
	);
};

// Footer component for websites
export interface WebsiteFooterProps {
	fullName: string;
	className?: string;
}

export const WebsiteFooter: React.FC<WebsiteFooterProps> = ({
	fullName,
	className = "",
}) => {
	return (
		<footer
			className={`website-footer bg-gray-50 py-8 text-center ${className}`}
		>
			<div className="max-w-6xl mx-auto px-4">
				<p className="text-gray-600 mb-2">
					© {new Date().getFullYear()} {fullName}
				</p>
				<p className="text-gray-500 text-sm">
					Created with{" "}
					<a
						href="https://quickcv.com"
						className="text-blue-600 hover:text-blue-800"
						target="_blank"
						rel="noopener noreferrer"
					>
						QuickCV
					</a>
				</p>
			</div>
		</footer>
	);
};
